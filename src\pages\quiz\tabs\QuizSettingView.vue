<template>
  <div class="row justify-center q-mt-xl">
    <q-card class="q-pa-xl custom-card">
      <div class="text-h4 q-mb-lg text-weight-bold">ตั้งค่า</div>

      <div class="q-gutter-md column">
        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">กำหนดขอบเขตเวลา</div>
            <div class="text-caption text-grey">
              กำหนดวันที่และเวลาเพื่อเปิด-ปิดแบบทดสอบแบบอัตโนมัติ
            </div>
          </div>

          <div class="row q-gutter-sm item-center" style="margin-top: 20px">
            <SelectDate
              v-model:model-value="selectedDate"
              label="เลือกวันเริ่มต้น"
              style="margin-right: 20px"
            />
            <SelectDate
              v-model:model-value="selectedDateEnd"
              label="เลือกวันสิ้นสุด"
              style="margin-right: 20px"
              :disable="selectedDate === '' || selectedDate === null"
              :rules="[
                (val: any) =>
                  !val ||
                  val >= selectedDate ||
                  'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น',
              ]"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ตั้งเวลาทำแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเวลาในการทำแบบทดสอบของผู้ทำแบบสอบถาม</div>
          </div>
          <div class="row q-col-gutter-sm items-center">
            <HourDropdown v-model:model-value="hour" />
            <MinDropdown v-model:model-value="minute" style="margin-right: 30px" />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">จำนวนครั้งที่สามารถทำแบบทดสอบ</div>
            <div class="text-caption text-grey">
              กำหนดจำนวนครั้งที่สามารถทำแบบทดสอบของผู้ทำแบบสอบถาม
            </div>
          </div>
          <div style="min-width: 300px">
            <TextField
              v-model:model-value="attemptLimit"
              placeholder="กรุณากรอกข้อมูล..."
              type="number"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ร้อยละขั้นต่ำเพื่อผ่านแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเกณฑ์คะแนนผ่านของแบบสอบถาม</div>
          </div>
          <div style="min-width: 300px">
            <TextField
              v-model:model-value="passRatio"
              placeholder="กรุณากรอกข้อมูล..."
              type="number"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">เป็นต้นแบบ</div>
            <div class="text-caption text-grey">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <div style="min-width: 80px">
            <Toggle v-model:model-value="isPrototype" />
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import SelectDate from 'src/components/common/SelectDate.vue';
import TextField from 'src/components/common/TextField.vue';
import HourDropdown from 'src/components/common/HourDropdown.vue';
import MinDropdown from 'src/components/common/MinDropdown.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';
import { debounce } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAssessmentStore } from 'src/stores/asm';

const route = useRoute();
const paramId = route.params.id as string;
const assessment = new AssessmentService('quiz');
const assessmentStore = useAssessmentStore();

const selectedDate = ref<string>('');
const selectedDateEnd = ref<string>('');
const hour = ref<number | null>(null);
const minute = ref<number | null>(null);
const attemptLimit = ref<number>(0);
const passRatio = ref<number>(0);
const isPrototype = ref<boolean>(false);

const convertSecondsToHourMinute = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return { hours, minutes };
};

const convertToSeconds = (hours: number, minutes: number) => {
  return hours * 3600 + minutes * 60;
};

const convertToDecimal = (percentage: number): number => {
  return percentage / 100;
};

const convertToPercentage = (decimal: number): number => {
  return decimal * 100;
};

const saveAssessment = debounce(async () => {
  if (assessmentStore.currentAssessment) {
    try {
      const assessmentToSave = { ...assessmentStore.currentAssessment };

      // แปลงวันที่เป็น Date หรือ null
      if (selectedDate.value) {
        assessmentToSave.startAt = selectedDate.value;
      } else {
        delete assessmentToSave.startAt;
      }

      if (selectedDateEnd.value) {
        assessmentToSave.endAt = selectedDateEnd.value;
      } else {
        delete assessmentToSave.endAt;
      }

      // จัดการ timeout
      if (hour.value !== null && minute.value !== null && hour.value >= 0 && minute.value >= 0) {
        assessmentToSave.timeout = convertToSeconds(hour.value, minute.value);
      } else {
        assessmentToSave.timeout = 0;
      }

      // จัดการ submitLimit
      assessmentToSave.submitLimit = Number(attemptLimit.value) || -1;

      // จัดการ passRatio
      if (passRatio.value !== undefined && passRatio.value !== null) {
        const validatedPercentage = Math.min(Math.max(Number(passRatio.value), 0), 100);
        assessmentToSave.passRatio = convertToDecimal(validatedPercentage);
      } else {
        assessmentToSave.passRatio = 0.5;
      }

      assessmentToSave.isPrototype = Boolean(isPrototype.value);

      const response = await assessment.updateOne(Number(paramId), assessmentToSave);
      console.log('Assessment saved successfully:', response);
    } catch (error) {
      console.error('Error saving assessment:', error);
    }
  } else {
    console.error('No assessment data to save');
  }
}, 500);

onMounted(async () => {
  try {
    const response = await assessment.fetchOne(Number(paramId));
    console.log('Assessment fetched:', response);
    assessmentStore.setCurrentAssessment(response);
    if (assessmentStore.currentAssessment) {
      selectedDate.value = assessmentStore.currentAssessment.startAt || '';
      selectedDateEnd.value = assessmentStore.currentAssessment.endAt || '';

      if (response.timeout) {
        const { hours, minutes } = convertSecondsToHourMinute(response.timeout);
        hour.value = hours ?? null;
        minute.value = minutes ?? null;
      } else {
        hour.value = null;
        minute.value = null;
      }

      attemptLimit.value = assessmentStore.currentAssessment.submitLimit ?? -1;
      passRatio.value = assessmentStore.currentAssessment.passRatio
        ? convertToPercentage(assessmentStore.currentAssessment.passRatio)
        : 0;
      isPrototype.value = assessmentStore.currentAssessment.isPrototype ?? false;
    } else {
      console.error('No assessment data found');
    }
  } catch (error) {
    console.error('Error fetching assessment:', error);
  }
});

onUnmounted(() => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.clearCurrentAssessment();
  }
});

watch(selectedDate, (newValue) => {
  if (assessmentStore.currentAssessment) {
    console.log('Start date changed:', newValue);
    assessmentStore.currentAssessment.startAt = newValue ? newValue : '';
    void saveAssessment();
  }
});

watch(selectedDateEnd, (newValue) => {
  if (assessmentStore.currentAssessment) {
    console.log('End date changed:', newValue);
    assessmentStore.currentAssessment.endAt = newValue ? newValue : '';
    void saveAssessment();
  }
});

watch([hour, minute], ([newHour, newMin]) => {
  if (assessmentStore.currentAssessment && newHour !== null && newMin !== null) {
    console.log('Time changed:', { hours: newHour, minutes: newMin });
    assessmentStore.currentAssessment.timeout = convertToSeconds(newHour, newMin);
    void saveAssessment();
  }
});

watch(attemptLimit, (newValue) => {
  if (assessmentStore.currentAssessment) {
    console.log('attemptLimit changed:', newValue);
    assessmentStore.currentAssessment.submitLimit = Number(newValue) || -1;
    void saveAssessment();
  }
});

watch(passRatio, (newValue) => {
  if (assessmentStore.currentAssessment && newValue !== undefined && newValue !== null) {
    console.log('passRatio changing to:', newValue);
    const validatedPercentage = Math.min(Math.max(Number(newValue), 0), 100);
    const decimalValue = convertToDecimal(validatedPercentage);
    assessmentStore.currentAssessment.passRatio = decimalValue;
    void saveAssessment();
  } else {
    console.warn('passRatio is undefined or null, skipping save');
  }
});

watch(isPrototype, (newValue) => {
  if (assessmentStore.currentAssessment) {
    console.log('Prototype status changed:', newValue);
    assessmentStore.currentAssessment.isPrototype = Boolean(newValue);
    void saveAssessment();
  }
});
</script>

<style scoped>
.custom-card {
  width: 1100px;
  height: 600px;
  border-radius: 12px;
  flex: none;
  order: 0;
  flex-grow: 0;
}
</style>
